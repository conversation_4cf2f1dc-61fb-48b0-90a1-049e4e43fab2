/**
 * editor.js - 自然語言點餐系統的編輯器功能
 * 提供 BDD 和 AAprompt 編輯器的語法高亮和驗證功能
 */

// 引入 lodash 的 debounce 函數
// 確保不重複宣告 debounce
let editorDebounce;
if (typeof _ !== 'undefined') {
  editorDebounce = _.debounce;
} else {
  editorDebounce = function(func, wait) {
    let timeout;
    return function() {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, arguments), wait);
    };
  };
}

// 初始化 CodeMirror 編輯器
let bddEditor, aaPromptEditor;

// BDD 關鍵字高亮規則
const bddKeywords = [
  "Feature:", "Scenario:", "Given", "When", "Then", "And", "But",
  "Background:", "Scenario Outline:", "Examples:"
];

// BDD 自定義模式
CodeMirror.defineMode("bdd", function() {
  return {
    token: function(stream) {
      if (stream.sol() && /Feature:|Scenario:|Background:|Scenario Outline:|Examples:/.test(stream.peek() + stream.match(/Feature:|Scenario:|Background:|Scenario Outline:|Examples:/, false))) {
        stream.match(/Feature:|Scenario:|Background:|Scenario Outline:|Examples:/);
        return "keyword";
      }
      
      if (stream.sol() && /Given|When|Then|And|But/.test(stream.peek() + stream.match(/Given|When|Then|And|But/, false))) {
        stream.match(/Given|When|Then|And|But/);
        return "atom";
      }
      
      // 字符串標記
      if (stream.match(/\"([^"]*)\"|\'([^']*)\'|`([^`]*)`/)) {
        return "string";
      }
      
      // 參數標記
      if (stream.match(/\$\w+/)) {
        return "variable";
      }
      
      // 跳過非關鍵字
      stream.next();
      return null;
    }
  };
});

// AAprompt 自定義模式
CodeMirror.defineMode("aaprompt", function() {
  return {
    token: function(stream) {
      // 標記角色和行為關鍵字
      if (stream.match(/作為|在|的情況下|，/)) {
        return "keyword";
      }
      
      // 標記變數和參數
      if (stream.match(/\{[^}]*\}/)) {
        return "variable";
      }
      
      // 跳過其他字符
      stream.next();
      return null;
    }
  };
});

// 初始化編輯器
function initializeEditors() {
  // 初始化 BDD 編輯器
  bddEditor = CodeMirror.fromTextArea(document.getElementById('bdd-text'), {
    mode: "bdd",
    lineNumbers: true,
    theme: "material",
    lineWrapping: true,
    tabSize: 2,
    extraKeys: {"Ctrl-Space": "autocomplete"}
  });
  
  // 初始化 AAprompt 編輯器
  aaPromptEditor = CodeMirror.fromTextArea(document.getElementById('aa-text'), {
    mode: "aaprompt",
    lineNumbers: true, 
    theme: "material",
    lineWrapping: true,
    tabSize: 2
  });
  
  // 設置自動完成功能
  CodeMirror.commands.autocomplete = function(cm) {
    CodeMirror.showHint(cm, CodeMirror.hint.bdd);
  };
  
  // 為 BDD 編輯器添加自定義自動完成
  CodeMirror.registerHelper("hint", "bdd", function(editor) {
    const cur = editor.getCursor();
    const token = editor.getTokenAt(cur);
    const line = editor.getLine(cur.line);
    const start = token.start;
    const end = cur.ch;
    const prefix = line.slice(start, end);
    
    const list = bddKeywords.filter(k => k.startsWith(prefix));
    
    return {
      list: list,
      from: CodeMirror.Pos(cur.line, start),
      to: CodeMirror.Pos(cur.line, end)
    };
  });
  
  // 添加實時語法驗證
  bddEditor.on("change", editorDebounce(validateBDD, 500));
  aaPromptEditor.on("change", editorDebounce(validateAAPrompt, 500));
  
  // 綁定儲存按鈕的事件處理
  setupEditorSaveButtons();
}

// 設置編輯器儲存按鈕功能
function setupEditorSaveButtons() {
  const saveBddBtn = document.getElementById('save-bdd');
  const saveAAPromptBtn = document.getElementById('save-aaprompt');
  
  if (saveBddBtn && bddEditor) {
    saveBddBtn.addEventListener('click', async function() {      try {
        const bddContent = bddEditor.getValue();
        if (!bddContent.trim()) {
          throw new Error(getTranslation('bdd_content_required'));
        }
        
        // 顯示儲存中狀態
        saveBddBtn.disabled = true;
        saveBddBtn.textContent = getTranslation('saving_bdd');
        
        console.log('正在儲存BDD內容...');
        
        // 發送儲存請求
        const response = await fetch('/api/prompt/save-bdd', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            bddText: bddContent
          })
        });
        
        if (!response.ok) {
          console.error('儲存請求失敗:', response.status, response.statusText);
          throw new Error(`儲存失敗 (${response.status}): ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('儲存BDD回應:', result);
          if (result.success) {
          showSuccess(getTranslation('bdd_save_success') || 'BDD 儲存成功！', 'bdd');
        } else {
          throw new Error(result.error || '儲存失敗');
        }      } catch (error) {
        console.error('儲存BDD錯誤:', error);
        showError(`${getTranslation('bdd_save_failed') || 'BDD 儲存失敗'}: ${error.message}`, 'bdd');
      }finally {
        saveBddBtn.disabled = false;
        saveBddBtn.textContent = getTranslation('save_bdd_btn');
      }
    });
    console.log('BDD編輯器儲存按鈕已綁定');
  }
  
  if (saveAAPromptBtn && aaPromptEditor) {
    saveAAPromptBtn.addEventListener('click', async function() {      try {
        const aaContent = aaPromptEditor.getValue();
        if (!aaContent.trim()) {
          throw new Error(getTranslation('aaprompt_content_required'));
        }
        
        // 顯示儲存中狀態
        saveAAPromptBtn.disabled = true;
        saveAAPromptBtn.textContent = getTranslation('saving_aaprompt');
        
        console.log('正在儲存AAprompt內容...');
        
        // 發送儲存請求
        const response = await fetch('/api/prompt/save-aaprompt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            aaPromptText: aaContent
          })
        });
        
        if (!response.ok) {
          console.error('儲存請求失敗:', response.status, response.statusText);
          throw new Error(`儲存失敗 (${response.status}): ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('儲存AAprompt回應:', result);
          if (result.success) {
          showSuccess(getTranslation('aaprompt_save_success') || 'AAprompt 儲存成功！', 'aaprompt');
        } else {
          throw new Error(result.error || '儲存失敗');
        }      } catch (error) {
        console.error('儲存AAprompt錯誤:', error);
        showError(`${getTranslation('aaprompt_save_failed') || 'AAprompt 儲存失敗'}: ${error.message}`, 'aaprompt');
      }finally {
        saveAAPromptBtn.disabled = false;
        saveAAPromptBtn.textContent = getTranslation('save_aaprompt_btn');
      }
    });
    console.log('AAprompt編輯器儲存按鈕已綁定');
  }
}

// BDD 語法驗證
async function validateBDD() {
  const bddText = bddEditor.getValue();
  if (!bddText.trim()) return;
  
  try {
    const response = await fetch('/api/prompt/validate-bdd', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ bddText })
    });
    
    const result = await response.json();
    
    if (result.success) {
      const validation = result.validation;
      
      if (!validation.valid) {
        showBDDErrors(validation.errors);
      } else {
        clearBDDErrors();
      }
    } else {
      console.error('BDD 驗證失敗:', result.error);
    }
  } catch (error) {
    console.error('BDD 驗證錯誤:', error);
  }
}

// 顯示 BDD 語法錯誤
function showBDDErrors(errors) {
  clearBDDErrors();
  
  const bddContainer = bddEditor.getWrapperElement();
  const errorDiv = document.createElement('div');
  errorDiv.className = 'editor-error';
  errorDiv.style.color = '#ff5252';
  errorDiv.style.padding = '10px';
  errorDiv.style.backgroundColor = 'rgba(255, 82, 82, 0.1)';
  errorDiv.style.marginTop = '10px';
  errorDiv.style.borderRadius = '4px';
  
  let errorContent = `<strong>${getTranslation('bdd_syntax_error') || 'BDD 語法錯誤'}:</strong><ul>`;
  errors.forEach(err => {
    errorContent += `<li>${err}</li>`;
  });
  errorContent += '</ul>';
  
  errorDiv.innerHTML = errorContent;
  bddContainer.parentNode.insertBefore(errorDiv, bddContainer.nextSibling);
}

// 清除 BDD 錯誤顯示
function clearBDDErrors() {
  const errorDivs = document.querySelectorAll('.editor-error');
  errorDivs.forEach(div => div.remove());
}

// AAprompt 語法驗證
async function validateAAPrompt() {
  const aaText = aaPromptEditor.getValue();
  if (!aaText.trim()) return;
  
  try {
    const response = await fetch('/api/prompt/validate-aaprompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ aaText })
    });
    
    const result = await response.json();
    
    if (result.success) {
      const validation = result.validation;
      
      if (!validation.valid) {
        showAAPromptErrors(validation.errors);
      } else {
        clearAAPromptErrors();
      }
    } else {
      console.error('AAprompt 驗證失敗:', result.error);
    }
  } catch (error) {
    console.error('AAprompt 驗證錯誤:', error);
  }
}

// 顯示 AAprompt 語法錯誤
function showAAPromptErrors(errors) {
  clearAAPromptErrors();
  
  const aaContainer = aaPromptEditor.getWrapperElement();
  const errorDiv = document.createElement('div');
  errorDiv.className = 'editor-error-aa';
  errorDiv.style.color = '#ff5252';
  errorDiv.style.padding = '10px';
  errorDiv.style.backgroundColor = 'rgba(255, 82, 82, 0.1)';
  errorDiv.style.marginTop = '10px';
  errorDiv.style.borderRadius = '4px';
  
  let errorContent = `<strong>${getTranslation('aaprompt_syntax_error')}:</strong><ul>`;
  errors.forEach(err => {
    errorContent += `<li>${err}</li>`;
  });
  errorContent += '</ul>';
  
  errorDiv.innerHTML = errorContent;
  aaContainer.parentNode.insertBefore(errorDiv, aaContainer.nextSibling);
}

// 清除 AAprompt 錯誤顯示
function clearAAPromptErrors() {
  const errorDivs = document.querySelectorAll('.editor-error-aa');
  errorDivs.forEach(div => div.remove());
}

// 更新生成狀態顯示
function updateGenerateStatus(isGenerating) {
  const generateBtn = document.getElementById('generate-btn');
  const apppromptContainer = document.getElementById('appprompt-container');
  const generateError = document.getElementById('generate-error');
  
  if (generateBtn) {
    generateBtn.disabled = isGenerating;
    generateBtn.textContent = isGenerating ? getTranslation('generating') || '生成中...' : getTranslation('generate_appprompt') || '生成 APPprompt';
  }
  
  if (generateError) {
    generateError.style.display = 'none';
  }
  
  // 如果正在生成，顯示一個加載指示器
  if (isGenerating && apppromptContainer) {
    apppromptContainer.innerHTML = `
      <div class="generating-indicator">
        <div class="spinner"></div>
        <p class="generating-text">${getTranslation('generating_appprompt_wait') || '正在生成 APPprompt，請稍候...'}</p>
      </div>
    `;
  }
}

// APPPrompt 生成處理
async function generateAPPPrompt() {
  try {
    updateGenerateStatus(true);
    
    // 檢查編輯器是否初始化
    if (!bddEditor || !aaPromptEditor) {
      throw new Error(getTranslation('editor_not_initialized') || '編輯器尚未初始化，請先切換到 BDD 或 AAprompt 編輯器標籤頁');
    }
    
    // 獲取編輯器內容
    const bddContent = bddEditor.getValue().trim();
    const aaContent = aaPromptEditor.getValue().trim();
    
    // 檢查是否有內容可以生成
    if (!bddContent && !aaContent) {
      throw new Error(getTranslation('no_content_to_generate') || '請先在 BDD 或 AAprompt 編輯器中輸入內容');
    }
    
    // 優先使用 BDD 內容，其次使用 AAprompt 內容
    const useType = bddContent ? 'bdd' : 'aaprompt';
    const content = bddContent || aaContent;
      // 發送生成請求
    console.log(`${getTranslation('generating_appprompt_using') || '正在生成 APPprompt，使用'} ${useType} ${getTranslation('content') || '內容'}`);
    const response = await fetch('/api/prompt/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: useType,
        content: content,
        language: getCurrentLanguage() || 'zh-TW'
      })
    });
    
    if (!response.ok) {
      throw new Error(`${getTranslation('generation_failed') || '生成失敗'}: ${response.statusText}`);
    }
    
    const result = await response.json();    if (result.error) {
      throw new Error(result.error);
    }
    
    // 現在只在APPprompt生成器頁籤中顯示結果
    const container = document.getElementById('appprompt-container');
    if (container) {
      // 使用 showGeneratedAPPPrompt 函數顯示完整的 APPprompt 結果
      showGeneratedAPPPrompt(result.appPrompt, 'appprompt-container');
      showSuccess(getTranslation('appprompt_generation_success') || 'APPPrompt 生成成功！');
    }
    
  } catch (error) {
    console.error(getTranslation('generation_error') || '生成錯誤:', error);
    showError(`${getTranslation('appprompt_generation_error') || '生成APPprompt錯誤'}: ${error.message}`);
  } finally {
    updateGenerateStatus(false);
  }
}

// 生成 APPprompt 的狀態管理
let isGenerating = false;

function setGeneratingState(generating, message = getTranslation('generating_with_gemini') || '正在使用 USI AIOS 生成 APPprompt...') {
  isGenerating = generating;
  const generateBtn = document.getElementById('generate-btn');
  const resultDisplay = document.getElementById('app-prompt-result');
  
  if (generating) {
    generateBtn.disabled = true;
    generateBtn.classList.add('disabled');
    resultDisplay.innerHTML = `
      <div class="generating-indicator">
        <div class="spinner"></div>
        <p>${message}</p>
      </div>
    `;
  } else {
    generateBtn.disabled = false;
    generateBtn.classList.remove('disabled');
  }
}

// 處理生成APPprompt請求
async function processGenerateRequest(url, data) {
  // 取得結果顯示容器
  const container = document.getElementById('result-container');
  const promptContainer = document.getElementById('appprompt-container');
  
  try {
    // 顯示生成中狀態
    promptContainer.innerHTML = `
      <div class="generating-indicator">
        <div class="spinner"></div>
        <p class="generating-text">${getTranslation('analyzing_with_gemini') || '正在使用 USI AIOS 分析並生成 APPprompt...'}</p>
      </div>
    `;
    
    const generateBtn = document.getElementById('generate-btn');
    if (generateBtn) {
      generateBtn.disabled = true;
    }
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    
    if (generateBtn) {
      generateBtn.disabled = false;
    }
    
    if (result.success) {
      const aiGenerated = result.appPrompt.metadata.aiGenerated;
      showGeneratedAPPPrompt(result.appPrompt);
      showSuccess(aiGenerated ? getTranslation('gemini_generation_success') || 'USI AIOS 已成功生成 APPprompt' : getTranslation('appprompt_generation_success') || 'APPprompt 已成功生成');
    } else {
      showError(getTranslation('appprompt_generation_failed') || '生成 APPprompt 失敗: ' + result.error);
    }
  } catch (error) {
    if (generateBtn) {
      generateBtn.disabled = false;
    }
    showError(getTranslation('generation_process_error') || '生成過程中發生錯誤: ' + error.message);
  }
}

// 顯示模擬的APPprompt結果
function showMockAppPrompt() {
  // 更改為實際產生的結果結構
  const appPrompt = {
    prompt: `// 根據 Actor-Action 分析自然語言點餐需求
const processOrder = async (input, context) => {
  // 自然語言處理模組
  const nlpResult = await nlpModule.processInput(input);
  
  // 查詢模組
  const menuItems = await queryModule.findMenuItems(nlpResult.entities);
  
  // 回應生成模組
  const response = responseModule.generateResponse(menuItems, nlpResult);
  
  return response;
}`,
    parameters: {
      "restaurant_id": "mcdonalds", 
      "language": "zh-TW",
      "order_type": "dine-in",
      "recognition_confidence": 0.8
    },
    metadata: {
      "model": "natural-order-v1",
      "version": "1.0.0",
      "created_at": new Date().toISOString()
    }  };
  
  showGeneratedAPPPrompt(appPrompt, 'appprompt-container');
  
  // 同時顯示程式碼預覽
  const container = document.getElementById(containerId);
  container.innerHTML += `
    <h3 style="margin-top: 30px;">程式碼實現示例</h3>
    <div class="app-prompt-result">
      <h4>自然語言處理模組</h4>
      <pre><code class="language-javascript">${hljs.highlight(generateMockNlpModule(), {language: 'javascript'}).value}</code></pre>
      
      <h4>查詢模組</h4>
      <pre><code class="language-javascript">${hljs.highlight(generateMockQueryModule(), {language: 'javascript'}).value}</code></pre>
      
      <h4>回應生成模組</h4>
      <pre><code class="language-javascript">${hljs.highlight(generateMockResponseModule(), {language: 'javascript'}).value}</code></pre>
    </div>
  `;
}

// 顯示生成的 APPprompt
function showGeneratedAPPPrompt(appPrompt, containerId = 'appprompt-container') {
  const container = document.getElementById(containerId);
  
  if (!appPrompt) {
    container.innerHTML = `<div class="alert alert-warning">${getTranslation('generated_appprompt_empty') || '生成的 APPprompt 為空'}</div>`;
    return;
  }
  
  let html = `<h3>${getTranslation('generated_appprompt') || '生成的 APPprompt'}</h3>`;
  html += '<div class="app-prompt-result">';
  
  try {
    // 檢查 hljs (highlight.js) 是否可用
    const hljs = window.hljs || { highlight: (code, opts) => ({ value: code }) };
    
    // 格式化提示詞
    html += `<h4>${getTranslation('prompt') || '提示詞'}:</h4>`;
    if (appPrompt.prompt) {
      try {
        html += `<pre><code class="language-javascript">${hljs.highlight(appPrompt.prompt, {language: 'javascript'}).value}</code></pre>`;
      } catch (e) {
        html += `<pre><code>${appPrompt.prompt}</code></pre>`;
      }
    } else {
      html += `<p>${getTranslation('no_prompt_content') || '無提示詞內容'}</p>`;
    }
    
    // 格式化參數
    html += `<h4>${getTranslation('parameters') || '參數'}:</h4>`;
    if (appPrompt.parameters) {
      try {
        const jsonParams = JSON.stringify(appPrompt.parameters, null, 2);
        html += `<pre><code class="language-json">${hljs.highlight(jsonParams, {language: 'json'}).value}</code></pre>`;
      } catch (e) {
        html += `<pre><code>${JSON.stringify(appPrompt.parameters, null, 2)}</code></pre>`;
      }
    } else {
      html += `<p>${getTranslation('no_parameters_content') || '無參數內容'}</p>`;
    }
    
    // 格式化元數據
    html += `<h4>${getTranslation('metadata') || '元數據'}:</h4>`;
    if (appPrompt.metadata) {
      try {
        const jsonMeta = JSON.stringify(appPrompt.metadata, null, 2);
        html += `<pre><code class="language-json">${hljs.highlight(jsonMeta, {language: 'json'}).value}</code></pre>`;
      } catch (e) {
        html += `<pre><code>${JSON.stringify(appPrompt.metadata, null, 2)}</code></pre>`;
      }
    } else {
      html += `<p>${getTranslation('no_metadata_content') || '無元數據內容'}</p>`;
    }    html += '</div>';
    container.innerHTML = html;
    
    // 顯示主下載按鈕
    const downloadBtn = document.getElementById('download-btn');
    if (downloadBtn) {
      downloadBtn.style.display = 'inline-block';
    }
    
    // 嘗試自動套用 highlight.js 格式化
    try {
      if (typeof hljs.highlightAll === 'function') {
        hljs.highlightAll();
      } else if (container.querySelectorAll) {
        container.querySelectorAll('pre code').forEach(block => {
          try {
            hljs.highlightElement(block);
          } catch (e) {
            console.warn(getTranslation('code_highlight_failed') || '代碼高亮失敗:', e);
          }
        });
      }
    } catch (e) {
      console.warn(getTranslation('code_highlight_process_failed') || '代碼高亮處理失敗:', e);
    }
  } catch (error) {
    console.error(getTranslation('display_appprompt_error') || '顯示 APPprompt 錯誤:', error);
    container.innerHTML = `<div class="alert alert-danger">${getTranslation('display_appprompt_error_occurred') || '顯示 APPprompt 時發生錯誤'}: ${error.message}</div>`;
  }
    // 保存生成的APPprompt，以便下載
  window.generatedAPPPrompt = appPrompt;
}

// 下載 APPprompt
function downloadAPPPrompt() {
  if (!window.generatedAPPPrompt) {
    showError(getTranslation('generate_appprompt_first') || '請先生成 APPprompt');
    return;
  }
  
  const appPrompt = window.generatedAPPPrompt;
  
  // 將APPprompt格式化為JSON字符串
  const jsonContent = JSON.stringify({
    prompt: appPrompt.prompt,
    parameters: appPrompt.parameters,
    metadata: appPrompt.metadata
  }, null, 2);
  
  // 創建Blob對象
  const blob = new Blob([jsonContent], { type: 'application/json' });
  
  // 創建URL
  const url = URL.createObjectURL(blob);
  
  // 創建下載鏈接
  const downloadLink = document.createElement('a');
  downloadLink.href = url;
    // 使用當前本地日期和時間作為文件名 (依據用戶電腦的時區)
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const timestamp = `${year}${month}${day}${hours}${minutes}`;
  
  downloadLink.download = `appPrompt_${timestamp}.json`;
  
  // 模擬點擊
  document.body.appendChild(downloadLink);
  downloadLink.click();
  
  // 清理
  document.body.removeChild(downloadLink);
  URL.revokeObjectURL(url);
  
  showSuccess(getTranslation('appprompt_download_success') || 'APPprompt 已成功下載');
}

// 圖片載入和base64編碼處理
function handleImageUpload(files) {
  if (!files || files.length === 0) {
    console.warn(getTranslation('no_file_selected') || '未選擇檔案');
    return;
  }

  const file = files[0];
  if (!file.type.match('image.*')) {
    showError(getTranslation('please_select_image') || '請選擇圖片檔案');
    return;
  }

  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const base64Image = e.target.result;
      if (!base64Image.startsWith('data:image/')) {
        showError(getTranslation('invalid_image_format') || '圖片格式無效');
        return;
      }
      document.getElementById('preview-image').src = base64Image;
      document.getElementById('preview-image').style.display = 'block';
    } catch (error) {
      console.error(getTranslation('image_processing_error') || '圖片處理錯誤:', error);
      showError(getTranslation('image_processing_failed') || '圖片處理失敗');
    }
  };
  
  reader.onerror = function() {
    console.error(getTranslation('file_read_error') || '檔案讀取錯誤');
    showError(getTranslation('file_read_failed') || '檔案讀取失敗');
  };

  reader.readAsDataURL(file);
}

// 初始化拖放區域
function initializeDropArea() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const browseBtn = document.getElementById('browse-btn');
    
    if (dropArea && fileInput) {
        // 檢查是否已經初始化過，防止重複綁定事件
        if (dropArea.dataset.initialized === 'true') {
            console.log(getTranslation('drop_area_already_initialized') || '拖放區域已經初始化過，跳過');
            return;
        }
        
        // 標記為已初始化
        dropArea.dataset.initialized = 'true';
        
        // 拖放效果
        dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.style.borderColor = '#4CAF50';
        });
        
        dropArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.style.borderColor = '#ddd';
        });
        
        dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.style.borderColor = '#ddd';
            console.log(getTranslation('file_drop_triggered') || '檔案拖放事件觸發');
            
            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                const file = e.dataTransfer.files[0];
                // 直接設置檔案到 input
                updateFileInput(fileInput, e.dataTransfer.files);
                // 更新顯示
                const selectedFileText = getTranslation('file_selected') || '已選擇檔案';
                const fileSizeText = getTranslation('file_size') || '檔案大小';
                dropArea.innerHTML = `<p>${selectedFileText}：${file.name}</p><p class="small">${fileSizeText}：${formatFileSize(file.size)}</p>`;
                
                // 清除任何錯誤訊息
                const errorMessage = document.getElementById('error-message');
                if (errorMessage) {
                    errorMessage.style.display = 'none';
                }
                
                // 啟用上傳按鈕
                const uploadBtn = document.getElementById('upload-btn');
                if (uploadBtn) {
                    uploadBtn.disabled = false;
                }
                
                // 不要自動提交
            }        });
        
        // 點擊拖放區域，保留這個事件，但不阻止冒泡，允許事件傳遞到file-input
        console.log(getTranslation('ensure_drop_area_click') || '確保拖放區域點擊事件能觸發檔案選擇對話框');
          // 檔案選擇變更
        fileInput.addEventListener('change', (e) => {
            console.log(getTranslation('file_selection_triggered') || '檔案選擇事件觸發');
            if (e.target && e.target.files && e.target.files.length > 0) {
                const file = e.target.files[0];
                // 更新顯示
                const selectedFileText = getTranslation('file_selected') || '已選擇檔案';
                const fileSizeText = getTranslation('file_size') || '檔案大小';
                dropArea.innerHTML = `<p>${selectedFileText}：${file.name}</p><p class="small">${fileSizeText}：${formatFileSize(file.size)}</p>`;
                
                // 清除任何錯誤訊息
                const errorMessage = document.getElementById('error-message');
                if (errorMessage) {
                    errorMessage.style.display = 'none';
                }
                
                // 顯示成功消息
                const fileSelectedSuccessMsg = getTranslation('file_selected_success') || '檔案已成功選擇，請點擊「上傳菜單」按鈕完成上傳';
                showSuccess(fileSelectedSuccessMsg);
                
                // 啟用上傳按鈕
                const uploadBtn = document.getElementById('upload-btn');
                if (uploadBtn) {
                    uploadBtn.disabled = false;
                }
            }
        });
    }
    
    // 確保瀏覽按鈕能觸發檔案選擇
    if (browseBtn && fileInput) {
        browseBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log(getTranslation('browse_button_clicked') || '點擊瀏覽按鈕，觸發檔案選擇對話框');
            fileInput.click();
        });
    }
}

// 格式化檔案大小（KB, MB等）
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
}

// 輔助函數：更新檔案輸入
function updateFileInput(fileInput, files) {
    try {
        // 建立一個新的 DataTransfer 物件
        const dataTransfer = new DataTransfer();
        // 新增檔案
        for (let i = 0; i < files.length; i++) {
            dataTransfer.items.add(files[i]);
        }
        // 設置檔案
        fileInput.files = dataTransfer.files;
    } catch (error) {
        console.error(getTranslation('set_file_failed') || '設置檔案失敗:', error);
    }
}

// 初始化圖片上傳
function initializeImageUpload() {
    const imageInput = document.getElementById('image-upload');
    if (imageInput) {
        imageInput.addEventListener('change', function(event) {
            handleImageUpload(event.target.files);
        });
    }
}

// 顯示錯誤訊息（與 index.html 中兼容）
function showError(message, context) {
    // 根據當前活躍的標籤頁選擇合適的消息元素
    let errorDiv = null;
    let errorContent = null;
    
    // 確定當前處於哪個標籤頁
    const activeTab = document.querySelector('.tab.active');
    let tabId = activeTab ? activeTab.dataset.tab : '';
    
    // 如果提供了上下文，使用它來確定要使用哪個消息元素
    if (context === 'bdd') {
        errorDiv = document.getElementById('bdd-error-message');
        errorContent = document.getElementById('bdd-error-content');
    } else if (context === 'aaprompt') {
        errorDiv = document.getElementById('aa-error-message');
        errorContent = document.getElementById('aa-error-content');
    } else if (tabId === 'bdd-editor') {
        errorDiv = document.getElementById('bdd-error-message');
        errorContent = document.getElementById('bdd-error-content');
    } else if (tabId === 'aa-prompt-editor') {
        errorDiv = document.getElementById('aa-error-message');
        errorContent = document.getElementById('aa-error-content');
    } else {
        // 默認使用 menu-management 的消息元素
        errorDiv = document.getElementById('error-message');
        errorContent = document.getElementById('error-content');
    }
    
    // 如果找不到特定元素，則使用通用的 error-message
    if (!errorDiv) {
        errorDiv = document.getElementById('error-message');
        errorContent = document.getElementById('error-content');
    }
    
    if (errorDiv) {
        // 如果找到error-content元素，設置其內容，否則設置整個errorDiv的內容
        if (errorContent) {
            errorContent.textContent = message;
        } else {
            errorDiv.innerHTML = `<strong><i class="fas fa-exclamation-circle"></i> ${getTranslation('error') || '錯誤'}！</strong> ${message}`;
        }
        
        // 顯示並設置淡出效果
        errorDiv.style.display = 'block';
        errorDiv.style.opacity = '1';
        errorDiv.style.transition = 'opacity 0.5s ease-in-out';
        
        // 如果已有計時器，清除它
        if (errorDiv._timeoutId) {
            clearTimeout(errorDiv._timeoutId);
        }
        
        // 設置新計時器，5秒後淡出
        errorDiv._timeoutId = setTimeout(() => {
            errorDiv.style.opacity = '0';
            setTimeout(() => {
                errorDiv.style.display = 'none';
                errorDiv.style.opacity = '1'; // 重置為下次顯示做準備
            }, 500); // 0.5秒後完全隱藏
        }, 5000);
        
        // 同時顯示一個浮動提示（如果存在）
        if (typeof showToastMessage === 'function') {
            showToastMessage(`<strong>${getTranslation('error') || '錯誤'}</strong><br>${message}`, 'error');
        }
    }
    console.error(getTranslation('error') || '錯誤:', message);
}

// 顯示成功訊息（與 index.html 中兼容）
function showSuccess(message, context) {
    // 根據當前活躍的標籤頁選擇合適的消息元素
    let successDiv = null;
    let successContent = null;
    
    // 確定當前處於哪個標籤頁
    const activeTab = document.querySelector('.tab.active');
    let tabId = activeTab ? activeTab.dataset.tab : '';
    
    // 如果提供了上下文，使用它來確定要使用哪個消息元素
    if (context === 'bdd') {
        successDiv = document.getElementById('bdd-success-message');
        successContent = document.getElementById('bdd-success-content');
    } else if (context === 'aaprompt') {
        successDiv = document.getElementById('aa-success-message');
        successContent = document.getElementById('aa-success-content');
    } else if (tabId === 'bdd-editor') {
        successDiv = document.getElementById('bdd-success-message');
        successContent = document.getElementById('bdd-success-content');
    } else if (tabId === 'aa-prompt-editor') {
        successDiv = document.getElementById('aa-success-message');
        successContent = document.getElementById('aa-success-content');
    } else {
        // 默認使用 menu-management 的消息元素
        successDiv = document.getElementById('success-message');
        successContent = document.getElementById('success-content');
    }
    
    // 如果找不到特定元素，則使用通用的 success-message
    if (!successDiv) {
        successDiv = document.getElementById('success-message');
        successContent = document.getElementById('success-content');
    }
    
    if (successDiv) {
        // 如果找到success-content元素，設置其內容，否則設置整個successDiv的內容
        if (successContent) {
            successContent.textContent = message;
        } else {
            successDiv.innerHTML = `<strong><i class="fas fa-check-circle"></i> ${getTranslation('success') || '成功'}！</strong> ${message}`;
        }
        
        // 顯示並設置淡出效果
        successDiv.style.display = 'block';
        successDiv.style.opacity = '1';
        successDiv.style.transition = 'opacity 0.5s ease-in-out';
        
        // 如果已有計時器，清除它
        if (successDiv._timeoutId) {
            clearTimeout(successDiv._timeoutId);
        }
        
        // 設置新計時器，5秒後淡出
        successDiv._timeoutId = setTimeout(() => {
            successDiv.style.opacity = '0';
            setTimeout(() => {
                successDiv.style.display = 'none';
                successDiv.style.opacity = '1'; // 重置為下次顯示做準備
            }, 500); // 0.5秒後完全隱藏
        }, 5000);
        
    }
    console.log(getTranslation('success') || '成功:', message);
}

// 更新預覽區域（與 index.html 中兼容）
function updatePreview(data) {
    const previewContainer = document.getElementById('preview-container');
    if (!previewContainer || !data) return;

    // 預覽邏輯，與 index.html 保持一致
    console.log(getTranslation('update_preview') || '更新預覽:', data);
}

// 在 DOMContentLoaded 時初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(getTranslation('initializing_editor_upload') || '初始化編輯器和上傳功能...');
    
    // 初始化編輯器
    if (typeof initializeEditors === 'function') {
        initializeEditors();
    }
    
    // 初始化拖放區域
    initializeDropArea();
    
    // 初始化圖片上傳
    initializeImageUpload();      // 綁定 APPprompt 生成按鈕點擊事件
    const generateBtn = document.getElementById('generate-btn');
    if (generateBtn) {
        console.log(getTranslation('found_generate_btn') || '找到 generate-btn 按鈕，綁定點擊事件');
        generateBtn.addEventListener('click', generateAPPPrompt);
    } else {
        console.error(getTranslation('generate_btn_not_found') || '找不到 generate-btn 按鈕');
    }
    
    // 綁定主下載按鈕的事件處理
    const downloadBtn = document.getElementById('download-btn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', downloadAPPPrompt);
    }
    
    // 不再嘗試調用setupUploadButton，因為它在index.html中定義
    // 而不是在editor.js中
    
    // 進行一些診斷日誌
    const uploadBtn = document.getElementById('upload-btn');
    const fileInput = document.getElementById('file-input');
    const dropArea = document.getElementById('drop-area');
    
    console.log(getTranslation('upload_btn_exists') || '上傳按鈕存在:', !!uploadBtn);
    console.log(getTranslation('file_input_exists') || '檔案輸入框存在:', !!fileInput);
    console.log(getTranslation('drop_area_exists') || '拖放區域存在:', !!dropArea);
});
