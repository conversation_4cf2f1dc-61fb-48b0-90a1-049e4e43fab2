# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 編譯輸出
dist

# 環境變數文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日誌文件
logs
*.log

# 運行時數據
pids
*.pid
*.seed
*.pid.lock

# 覆蓋率目錄
coverage

# IDE 文件
.vscode
.idea
*.swp
*.swo
*~

# OS 生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore

# 文檔
*.md

# 測試文件
*.test.js
*.test.ts
__tests__

# 臨時文件
*.tmp
*.temp