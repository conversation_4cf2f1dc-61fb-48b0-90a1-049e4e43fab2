import { BDDSpec, AAPrompt, APPPromptResult, MenuData } from '../types/menu.js';
import { NLPService } from './NLPService.js';
import { GeminiService } from './GeminiService.js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// 加載環境變數
dotenv.config();

/**
 * APPprompt 生成引擎
 * 用於將 BDD、AAprompt 或自然語言轉換為 APPprompt 格式
 */
export class PromptEngine {
  private nlpService: NLPService;
  private geminiService: GeminiService | null = null;
  private useAI: boolean;
  private menuData: MenuData | null = null; // 全局菜單數據存儲
  
  constructor() {
    this.nlpService = new NLPService();
    this.useAI = process.env.USE_GEMINI_AI === 'true';
    
    // 僅在啟用 AI 時初始化 GeminiService
    if (this.useAI) {
      try {
        this.geminiService = new GeminiService();
      } catch (error) {
        console.error('初始化 Gemini AI 服務失敗:', error);
        this.useAI = false;
      }
    }
  }
    /**
   * 從 BDD 規範生成 APPprompt
   * @param bddSpec BDD 規範
   * @param language 語言設定
   */  async generateFromBDD(bddSpec: BDDSpec, language?: string): Promise<APPPromptResult> {
    if (this.useAI && this.geminiService) {
      try {
        return await this.geminiService.generateFromBDD(bddSpec, this.menuData, language);
      } catch (error) {
        console.error('使用 Gemini AI 生成 APPprompt 失敗:', error);
        throw new Error('AI 服務不可用，且已移除舊版NLP後備方案');
      }
    }
    throw new Error('Gemini AI 未啟用，且已移除舊版NLP後備方案');
  }
  
  /**
   * 從 AAprompt 格式生成 APPprompt
   * @param aaPrompt AAprompt 格式
   * @param language 語言設定
   */  async generateFromAA(aaPrompt: AAPrompt, language?: string): Promise<APPPromptResult> {
    if (this.useAI && this.geminiService) {
      try {
        return await this.geminiService.generateFromAA(aaPrompt, this.menuData, language);
      } catch (error) {
        console.error('使用 Gemini AI 生成 APPprompt 失敗:', error);
        throw new Error('AI 服務不可用，且已移除舊版NLP後備方案');
      }
    }
    throw new Error('Gemini AI 未啟用，且已移除舊版NLP後備方案');
  }
    /**
   * 從自然語言生成 APPprompt
   * @param input 自然語言文本
   */  async generateFromNaturalLanguage(input: string): Promise<APPPromptResult> {
    if (this.useAI && this.geminiService) {
      try {
        return await this.geminiService.parseNaturalLanguageToAppPrompt(input, this.menuData);
      } catch (error) {
        console.error('使用 Gemini AI 生成 APPprompt 失敗:', error);
        throw new Error('AI 服務不可用，且已移除舊版NLP後備方案');
      }
    }
    throw new Error('Gemini AI 未啟用，且已移除舊版NLP後備方案');
  }
  /**
   * 使用動態生成的 APPprompt 處理顧客點餐請求
   * @param customerInput 顧客的點餐需求
   * @param generatedAppPrompt 動態生成的 APPprompt（可選，如果沒有會先嘗試加載最新的 APPprompt 文件）
   * @param language 語言設置（可選，預設為 'zh-TW'）
   */
  async processOrderWithDynamicPrompt(customerInput: string, generatedAppPrompt?: string, language: string = 'zh-TW'): Promise<string> {
    if (!this.useAI || !this.geminiService) {
      throw new Error('此功能需要啟用 AI 服務');
    }

    let appPromptToUse = generatedAppPrompt;

    // 如果沒有提供 APPprompt，先嘗試加載最新的 APPprompt 文件
    if (!appPromptToUse) {
      console.log('未提供 APPprompt，嘗試加載最新的 APPprompt 文件...');
      
      // 首先嘗試加載已存在的 APPprompt 文件
      const loadedAppPrompt = await this.loadLatestAppPrompt();
      
      if (loadedAppPrompt) {
        try {
          const appPromptData = JSON.parse(loadedAppPrompt);
          // 如果加載的 APPprompt 包含完整的菜單信息，使用它
          if (appPromptData.parameters && appPromptData.parameters.menu) {
            console.log(`成功加載 APPprompt，包含 ${appPromptData.parameters.menu.length} 個菜單類別`);
            
            // 構建包含完整菜單信息的 APPprompt
            let fullPrompt = appPromptData.prompt;
            
            // 添加菜單信息到提示中
            if (appPromptData.parameters.menu.length > 0) {
              fullPrompt += '\n\n可用菜單：\n';
              
              appPromptData.parameters.menu.forEach((category: any) => {
                fullPrompt += `\n【${category.category}】\n`;
                category.items.forEach((item: any) => {
                  let itemName = item.name_zh;
                  let itemPrice = item.price;
                  let currency = 'NT$';
                  
                  // 根據語言選擇對應的名稱和價格
                  if (language === 'en-US' && item.name_en) {
                    itemName = item.name_en;
                  } else if (language === 'ja-JP') {
                    if (item.name_jp) itemName = item.name_jp;
                    if (item.price_jp) {
                      itemPrice = item.price_jp;
                      currency = '¥';
                    }
                  }
                  
                  fullPrompt += `- ${itemName} (ID: ${item.id}): ${currency}${itemPrice}\n`;
                });
              });
            }
            
            appPromptToUse = fullPrompt;
          } else {
            console.log('加載的 APPprompt 不包含完整菜單信息，將重新生成');
            appPromptToUse = appPromptData.prompt;
          }
        } catch (error) {
          console.error('解析加載的 APPprompt 失敗:', error);
        }
      }
      
      // 如果仍然沒有可用的 APPprompt，基於當前菜單生成一個
      if (!appPromptToUse) {
        console.log(`未找到可用的 APPprompt 文件，正在基於當前菜單生成... (語言: ${language})`);
        
        const bddSpec: BDDSpec = {
          feature: '自然語言點餐系統',
          scenario: '顧客使用自然語言點餐',
          given: [
            '顧客在點餐頁面上',
            '系統已載入菜單數據',
            '顧客可以使用自然語言描述需求'
          ],
          when: ['顧客輸入點餐需求'],
          then: [
            '系統應該識別餐點項目',
            '計算正確的價格',
            '確認訂單內容',
            '提供友善的回應'
          ]
        };

        const appPromptResult = await this.generateFromBDD(bddSpec);
        appPromptToUse = appPromptResult.prompt;
        console.log('已生成新的動態 APPprompt');
      }
    }

    // 使用生成的 APPprompt 處理顧客需求
    try {
      console.log(`使用 APPprompt 處理訂單，提示長度: ${appPromptToUse.length} (語言: ${language})`);
      return await this.geminiService.processNaturalLanguageOrder(customerInput, this.menuData, appPromptToUse);
    } catch (error) {
      console.error('使用動態 APPprompt 處理訂單失敗:', error);
      // 回退到基本處理
      return await this.geminiService.processNaturalLanguageOrder(customerInput, this.menuData);
    }
  }
    /**
   * 根據輸入類型自動選擇相應的生成方法
   * @param input 輸入內容（可以是 BDD，AAprompt 或自然語言）
   * @param type 輸入類型
   */
  async generateAppPrompt(input: BDDSpec | AAPrompt | string, type: 'bdd' | 'aaprompt' | 'natural'): Promise<APPPromptResult> {
    switch (type) {
      case 'bdd':
        return await this.generateFromBDD(input as BDDSpec);
      case 'aaprompt':
        return await this.generateFromAA(input as AAPrompt);
      case 'natural':
        return await this.generateFromNaturalLanguage(input as string);
      default:
        throw new Error(`不支持的輸入類型: ${type}`);
    }
  }
  
  /**
   * 驗證 BDD 語法
   * @param bddString BDD 字符串
   */
  validateBDDSyntax(bddString: string): {
    valid: boolean;
    spec?: BDDSpec;
    errors?: string[];
  } {
    try {
      // 解析 BDD 字符串
      const lines = bddString.split('\n').map(line => line.trim()).filter(Boolean);
      const errors: string[] = [];
      const spec: Partial<BDDSpec> = {};
      
      // 尋找 Feature 和 Scenario
      for (const line of lines) {
        if (line.toLowerCase().startsWith('feature:')) {
          spec.feature = line.substring('feature:'.length).trim();
        } else if (line.toLowerCase().startsWith('scenario:')) {
          spec.scenario = line.substring('scenario:'.length).trim();
        }
      }
      
      if (!spec.feature) {
        errors.push('未找到 Feature 定義');
      }
      
      if (!spec.scenario) {
        errors.push('未找到 Scenario 定義');
      }
      
      // 解析 Given, When, Then
      spec.given = [];
      spec.when = [];
      spec.then = [];
      
      let currentSection: 'given' | 'when' | 'then' | null = null;
      
      for (const line of lines) {
        const lowerLine = line.toLowerCase();
        
        if (lowerLine.startsWith('given')) {
          currentSection = 'given';
          spec.given.push(line.substring('given'.length).trim());
        } else if (lowerLine.startsWith('when')) {
          currentSection = 'when';
          spec.when.push(line.substring('when'.length).trim());
        } else if (lowerLine.startsWith('then')) {
          currentSection = 'then';
          spec.then.push(line.substring('then'.length).trim());
        } else if (lowerLine.startsWith('and') && currentSection) {
          // And 子句
          switch (currentSection) {
            case 'given':
              spec.given.push(line.substring('and'.length).trim());
              break;
            case 'when':
              spec.when.push(line.substring('and'.length).trim());
              break;
            case 'then':
              spec.then.push(line.substring('and'.length).trim());
              break;
          }
        } else if (!lowerLine.startsWith('feature:') && !lowerLine.startsWith('scenario:') && line.trim()) {
          // 不以已知關鍵字開頭的非空行
          if (currentSection) {
            // 如果當前有活動的部分，則可能是前一行的延續
            switch (currentSection) {
              case 'given':
                spec.given[spec.given.length - 1] += ' ' + line.trim();
                break;
              case 'when':
                spec.when[spec.when.length - 1] += ' ' + line.trim();
                break;
              case 'then':
                spec.then[spec.then.length - 1] += ' ' + line.trim();
                break;
            }
          } else {
            // 未知行
            errors.push(`無法識別的語句: "${line}"`);
          }
        }
      }
      
      // 檢查是否有 Given, When, Then
      if (spec.given.length === 0) {
        errors.push('未找到 Given 語句');
      }
      
      if (spec.when.length === 0) {
        errors.push('未找到 When 語句');
      }
      
      if (spec.then.length === 0) {
        errors.push('未找到 Then 語句');
      }
      
      if (errors.length > 0) {
        return {
          valid: false,
          errors: errors
        };
      }
        return {
        valid: true,
        spec: spec as BDDSpec
      };
    } catch (error) {
      return {
        valid: false,
        errors: [error instanceof Error ? error.message : '未知錯誤']
      };
    }
  }
  
  /**
   * 驗證 AAprompt 語法
   * @param aaString AAprompt 字符串
   */  validateAAPromptSyntax(aaString: string): {
    valid: boolean;
    prompt?: AAPrompt;
    errors?: string[];
  } {
    try {
      const errors: string[] = [];
      const prompt: Partial<AAPrompt> = {};
      
      // 多語言正則表達式支援
      const patterns = {
        // 中文格式
        zh: {
          regex: /作為\s*(.+?)，\s*(.+?)(?:，\s*(?:在\s*(.+?)(?:\s*的情況下)?(?:，\s*(.+?))?|(.+?)))?$/,
          formatExample: '作為[角色]，[動作]，在[情境]的情況下，[限制條件]',
          errorMsg: '無法解析 AAprompt 格式，請確保遵循 "作為[角色]，[動作]，在[情境]的情況下，[限制條件]" 格式',
          actorMissing: '未提供角色',
          actionMissing: '未提供動作'
        },
        // 英文格式
        en: {
          regex: /As\s+(?:a\s+)?(.+?),\s*(.+?)(?:,\s*(?:in\s+(.+?)(?:\s+(?:situation|context|environment))?(?:,\s*(.+?))?|(.+?)))?$/i,
          formatExample: 'As [role], [action], in [context] situation, [constraints]',
          errorMsg: 'Unable to parse AAprompt format, please follow "As [role], [action], in [context] situation, [constraints]" format',
          actorMissing: 'Role not provided',
          actionMissing: 'Action not provided'
        },
        // 日文格式
        ja: {
          regex: /(.+?)として、\s*(.+?)(?:、\s*(?:(.+?)(?:の状況で|の環境で|において)?(?:、\s*(.+?))?|(.+?)))?$/,
          formatExample: '[役割]として、[動作]、[状況]の状況で、[制約条件]',
          errorMsg: 'AAprompt形式を解析できません。「[役割]として、[動作]、[状況]の状況で、[制約条件]」の形式に従ってください',
          actorMissing: '役割が提供されていません',
          actionMissing: '動作が提供されていません'
        }
      };
      
      let match = null;
      let usedPattern = null;
      
      // 嘗試匹配各種語言格式
      for (const [lang, pattern] of Object.entries(patterns)) {
        match = aaString.match(pattern.regex);
        if (match) {
          usedPattern = pattern;
          console.log(`AAprompt parsing (${lang}):`, { input: aaString, match });
          break;
        }
      }
      
      if (!match || !usedPattern) {
        // 根據輸入文字判斷可能的語言並返回對應錯誤訊息
        let errorMsg = patterns.zh.errorMsg; // 預設中文
        if (/[a-zA-Z]/.test(aaString) && !/[\u4e00-\u9fff]/.test(aaString)) {
          errorMsg = patterns.en.errorMsg;
        } else if (/[\u3040-\u309f\u30a0-\u30ff]/.test(aaString)) {
          errorMsg = patterns.ja.errorMsg;
        }
        
        return {
          valid: false,
          errors: [errorMsg]
        };
      }
      
      prompt.actor = match[1]?.trim();
      prompt.action = match[2]?.trim();
      
      // 處理情境和限制條件
      // match[3] = 情境（當有"在...的情況下"時）
      // match[4] = 限制條件（當有情境時的限制條件）
      // match[5] = 限制條件（當沒有情境時的限制條件）
      
      if (match[3]) {
        prompt.context = match[3].trim();
      }
      
      const constraintsStr = match[4]?.trim() || match[5]?.trim();
      if (constraintsStr) {
        prompt.constraints = constraintsStr.split(/[,，]/).map(c => c.trim()).filter(Boolean);
      }
      
      if (!prompt.actor) {
        errors.push(usedPattern.actorMissing);
      }
      
      if (!prompt.action) {
        errors.push(usedPattern.actionMissing);
      }
      
      if (errors.length > 0) {
        return {
          valid: false,
          errors: errors
        };
      }
      
      return {
        valid: true,
        prompt: prompt as AAPrompt
      };
    } catch (error) {
      return {
        valid: false,
        errors: [error instanceof Error ? error.message : '未知錯誤']
      };
    }
  }

  /**
   * 設定菜單數據
   * @param menu 菜單數據
   */
  setMenuData(menu: MenuData) {
    this.menuData = menu;
    console.log('已設定菜單數據，包含 ' + menu.categories.reduce((sum, cat) => sum + cat.items.length, 0) + ' 個項目');
  }
  
  /**
   * 獲取當前菜單數據
   */
  getMenuData(): MenuData | null {
    return this.menuData;
  }  /**
   * 加載最新的 APPprompt 文件
   * @returns APPprompt 內容或 null
   */
  async loadLatestAppPrompt(): Promise<string | null> {
    try {
      const workspaceDir = process.cwd();
      let appPromptFiles: { filename: string; path: string }[] = [];
      
      // 首先檢查 appPrompt 目錄
      const appPromptDir = path.join(workspaceDir, 'appPrompt');
      if (fs.existsSync(appPromptDir)) {
        console.log('檢查 appPrompt 目錄中的 APPprompt 文件');
        const dirFiles = fs.readdirSync(appPromptDir);
        const dirAppPromptFiles = dirFiles
          .filter(file => file.startsWith('appPrompt_') && file.endsWith('.json'))
          .map(file => ({ 
            filename: file, 
            path: path.join(appPromptDir, file) 
          }));
        
        appPromptFiles.push(...dirAppPromptFiles);
      }
      
      // 然後檢查根目錄
      console.log('檢查根目錄中的 APPprompt 文件');
      const rootFiles = fs.readdirSync(workspaceDir);
      const rootAppPromptFiles = rootFiles
        .filter(file => file.startsWith('appPrompt_') && file.endsWith('.json'))
        .map(file => ({ 
          filename: file, 
          path: path.join(workspaceDir, file) 
        }));
      
      appPromptFiles.push(...rootAppPromptFiles);
      
      if (appPromptFiles.length === 0) {
        console.log('未找到 APPprompt 文件');
        return null;
      }
      
      // 按文件名排序，取最新的文件
      appPromptFiles.sort((a, b) => b.filename.localeCompare(a.filename));
      const latestFile = appPromptFiles[0];
      
      console.log(`加載 APPprompt 文件: ${latestFile.filename}`);
      
      const fileContent = fs.readFileSync(latestFile.path, 'utf8');
      const appPromptData = JSON.parse(fileContent);
      
      // 返回完整的 APPprompt 作為字符串，包含菜單信息
      return JSON.stringify(appPromptData);
      
    } catch (error) {
      console.error('加載 APPprompt 文件失敗:', error);
      return null;
    }
  }
}
