{"prompt": "你是一個自然語言點餐系統，根據顧客的語音或文字輸入，識別餐點、查詢菜單數據、確認訂單並處理取消訂單等操作。", "parameters": {"feature": "自然語言點餐", "scenario": "顧客取消訂單", "given": ["顧客在點餐頁面上", "系統已顯示識別出的餐點以供確認", "系統已顯示最終訂單以供確認 (大麥克套餐, 可樂)", "系統已顯示識別出的餐點以供確認 (大麥克套餐, 玉米湯)"], "when": ["顧客在自然語言輸入框中輸入 \"我想點一個大麥克套餐和一杯玉米湯\"", "顧客使用語音輸入說 \"我要一個麥脆雞兩塊餐\"", "顧客說 \"玉米湯換成可樂\"", "顧客輸入 \"來一份神秘魔法漢堡\"", "顧客說 \"再加一份薯條\"", "顧客說 \"玉米湯不要了\"", "顧客輸入 \"我要一個吉士漢堡和一個不知道什麼派\"", "顧客輸入 \"大麥克是什麼\"", "顧客說 \"確認\" 或 \"沒問題\"", "顧客說 \"取消\" 或 \"我不要了\""], "then": ["系統應該識別 \"大麥克套餐\" 和 \"玉米湯\"", "查詢數據庫/RAG 以獲取 \"大麥克套餐\" 和 \"玉米湯\" 的詳細信息（名稱、價格、圖片、供應情況）", "使用自然語言向顧客顯示已識別的餐點及其詳細信息以進行確認（例如：\"您是想點一份大麥克套餐和一份玉米湯嗎？\")", "系統應將語音輸入轉錄為文本", "識別 \"麥脆雞兩塊餐\"", "查詢數據庫/RAG 以獲取 \"麥脆雞兩塊餐\" 的詳細信息（名稱、價格、圖片、供應情況）", "使用自然語言向顧客顯示已識別的餐點及其詳細信息以進行確認（例如：\"您選的是一份麥脆雞兩塊餐，對嗎？\")", "系統應從建議的訂單中移除 \"玉米湯\"", "查詢數據庫/RAG 以獲取 \"可樂\" 的詳細信息", "使用自然語言向顧客顯示修改後的訂單（大麥克套餐，可樂）以進行確認", "系統應指示找不到 \"神秘魔法漢堡\" 這個餐點", "建議檢查菜單或重新措辭請求", "系統應該識別 \"薯條\"", "查詢數據庫/RAG 以獲取 \"薯條\" 的詳細信息", "將 \"薯條\" 添加到建議的訂單中", "使用自然語言向顧客顯示更新後的訂單（大麥克套餐，薯條）以進行確認（例如：\"好的，幫您加了一份薯條，現在是包含大麥克套餐和薯條，請問確認嗎？\")", "系統應從建議的訂單中移除 \"玉米湯\"", "使用自然語言向顧客顯示更新後的訂單（大麥克套餐）以進行確認（例如：\"好的，已經移除玉米湯，目前是只有大麥克套餐，請問確認嗎？\")", "系統應該識別 \"吉士漢堡\"", "指示找不到 \"不知道什麼派\" 這個餐點", "建議檢查菜單或重新措辭對未知餐點的請求", "系統應查詢數據庫/RAG 以獲取 \"大麥克\" 的詳細信息（描述、成分等）", "使用自然語言向顧客顯示有關 \"大麥克\" 的信息（例如：\"大麥克是一款經典的雙層牛肉漢堡，包含...\"）", "系統應處理訂單", "顯示訂單確認消息（例如：\"您的訂單已送出！\")", "系統應取消當前訂單", "顯示一條消息，指示取消（例如：\"好的，您的訂單已取消。\")"], "menu": [{"category": "炸雞類", "items": [{"id": "13", "name_zh": "2塊咔啦脆雞/上校薄脆雞/上校薄皮嫩雞", "name_en": "2-pc <PERSON><PERSON><PERSON>/Colonel's <PERSON><PERSON><PERSON>/Colonel's <PERSON>der Chicken", "price": 124, "category": "炸雞類", "name_jp": "カーネルクリスピー/オリジナルチキン 2ピース", "price_jp": 558}, {"id": "14", "name_zh": "2塊青花椒香麻脆雞", "name_en": "2-pc Sichuan Peppercorn Crispy Chicken", "price": 138, "category": "炸雞類", "name_jp": "四川花椒風味クリスピーチキン 2ピース", "price_jp": 621}, {"id": "3", "name_zh": "上校薄皮嫩雞（不辣）", "name_en": "Colonel's Tender Chicken (Original)", "price": 124, "category": "炸雞類", "name_jp": "オリジナルチキン（オリジナル）", "price_jp": 558}, {"id": "2", "name_zh": "上校薄脆雞（不辣）", "name_en": "Colonel's <PERSON><PERSON><PERSON> (Original)", "price": 124, "category": "炸雞類", "name_jp": "カーネルクリスピー（オリジナル）", "price_jp": 558}, {"id": "12", "name_zh": "原味起司燻雞捲", "name_en": "Original Cheese Smoked Chicken Wrap", "price": 73, "category": "炸雞類", "name_jp": "オリジナルチーズスモークチキンラップ", "price_jp": 328}, {"id": "1", "name_zh": "咔啦脆雞（辣）", "name_en": "Crispy Chicken (Spicy)", "price": 124, "category": "炸雞類", "name_jp": "カーネルクリスピー（辛口）", "price_jp": 558}, {"id": "20", "name_zh": "咔啦雞腿堡(辣)", "name_en": "Crispy Chicken Burger (Spicy)", "price": 115, "category": "炸雞類", "name_jp": "クリスピーチキンバーガー（辛口）", "price_jp": 518}, {"id": "15", "name_zh": "單點咔啦脆雞/上校薄脆雞/上校薄皮嫩雞", "name_en": "1-pc <PERSON><PERSON><PERSON>/Colonel's <PERSON><PERSON><PERSON>/Colonel's Tender Chicken", "price": 71, "category": "炸雞類", "name_jp": "カーネルクリスピー/オリジナルチキン 1ピース", "price_jp": 320}, {"id": "16", "name_zh": "單點青花椒香麻脆雞", "name_en": "1-pc Sichuan Peppercorn Crispy Chicken", "price": 78, "category": "炸雞類", "name_jp": "四川花椒風味クリスピーチキン 1ピース", "price_jp": 351}, {"id": "18", "name_zh": "川麻湘辣紙包雞", "name_en": "Sichuan-Hunan Spicy Paper-Wrapped Chicken", "price": 153, "category": "炸雞類", "name_jp": "四川湖南風辛口包み焼きチキン", "price_jp": 688}, {"id": "8", "name_zh": "紐奧良烤雞腿堡", "name_en": "New Orleans Grilled Chicken Burger", "price": 95, "category": "炸雞類", "name_jp": "ニューオーリンズグリルチキンバーガー", "price_jp": 428}, {"id": "5", "name_zh": "義式香草紙包雞", "name_en": "Italian Herb Paper-Wrapped Chicken", "price": 131, "category": "炸雞類", "name_jp": "イタリアンハーブ包み焼きチキン", "price_jp": 590}, {"id": "7", "name_zh": "花生熔岩咔啦雞腿堡", "name_en": "Peanut Lava Crispy Chicken Burger", "price": 127, "category": "炸雞類", "name_jp": "ピーナッツラバクリスピーチキンバーガー", "price_jp": 572}, {"id": "11", "name_zh": "花生起司雞柳捲", "name_en": "Peanut Cheese Chicken Strip Wrap", "price": 73, "category": "炸雞類", "name_jp": "ピーナッツチーズチキンストリップラップ", "price_jp": 328}, {"id": "21", "name_zh": "莎莎咔啦肉霸堡", "name_en": "Salsa Crispy Meat King Burger", "price": 159, "category": "炸雞類", "name_jp": "サルサクリスピーミートキングバーガー", "price_jp": 716}, {"id": "19", "name_zh": "雙層咔啦雞腿堡", "name_en": "Double Crispy Chicken Burger", "price": 165, "category": "炸雞類", "name_jp": "ダブルクリスピーチキンバーガー", "price_jp": 742}, {"id": "17", "name_zh": "雪花起司脆雞", "name_en": "Snowflake Cheese Crispy Chicken", "price": 75, "category": "炸雞類", "name_jp": "スノーフレークチーズクリスピーチキン", "price_jp": 338}, {"id": "6", "name_zh": "青花椒香麻咔啦雞腿堡", "name_en": "Sichuan Peppercorn Crispy Chicken Burger", "price": 105, "category": "炸雞類", "name_jp": "四川花椒風味クリスピーチキンバーガー", "price_jp": 472}, {"id": "4", "name_zh": "青花椒香麻脆雞（辣）", "name_en": "Sichuan Peppercorn Crispy Chicken (Spicy)", "price": 138, "category": "炸雞類", "name_jp": "四川花椒風味クリスピーチキン（辛口）", "price_jp": 621}, {"id": "9", "name_zh": "黃金魚子海陸Q蝦堡", "name_en": "Golden Roe Surf & Turf Shrimp Burger", "price": 145, "category": "炸雞類", "name_jp": "ゴールデンロー海鮮エビバーガー", "price_jp": 652}, {"id": "10", "name_zh": "墨西哥莎莎堡", "name_en": "Mexican Salsa Burger", "price": 105, "category": "炸雞類", "name_jp": "メキシカンサルサバーガー", "price_jp": 472}]}, {"category": "個人餐", "items": [{"id": "41", "name_zh": "上校薄脆雞XL全明星餐", "name_en": "Colonel's <PERSON><PERSON>py Chicken XL All-Star Meal", "price": 600, "category": "個人餐", "name_jp": "カーネルクリスピー XLオールスターセット", "price_jp": 2700}, {"id": "30", "name_zh": "吮指雙雞L絕配餐", "name_en": "Finger Lickin' Double Chicken L Perfect Meal", "price": 189, "category": "個人餐", "name_jp": "フィンガーリッキン ダブルチキン Lパーフェクトセット", "price_jp": 850}, {"id": "31", "name_zh": "吮指雙雞XL超豪肯！餐", "name_en": "Finger Lickin' Double Chicken XL Deluxe Meal", "price": 229, "category": "個人餐", "name_jp": "フィンガーリッキン ダブルチキン XLデラックスセット", "price_jp": 1030}, {"id": "36", "name_zh": "咔啦雞腿堡L絕配餐", "name_en": "Crispy Chicken Burger L Perfect Meal", "price": 160, "category": "個人餐", "name_jp": "クリスピーチキンバーガー Lパーフェクトセット", "price_jp": 720}, {"id": "37", "name_zh": "咔啦雞腿堡XL超豪肯！餐", "name_en": "Crispy Chicken Burger XL Deluxe Meal", "price": 210, "category": "個人餐", "name_jp": "クリスピーチキンバーガー XLデラックスセット", "price_jp": 945}, {"id": "28", "name_zh": "義式香草紙包雞套餐L絕配餐", "name_en": "Italian Herb Paper-Wrapped Chicken L Perfect Meal", "price": 200, "category": "個人餐", "name_jp": "イタリアンハーブ包み焼きチキン Lパーフェクトセット", "price_jp": 900}, {"id": "29", "name_zh": "義式香草紙包雞套餐XL超豪肯！餐", "name_en": "Italian Herb Paper-Wrapped Chicken XL Deluxe Meal", "price": 234, "category": "個人餐", "name_jp": "イタリアンハーブ包み焼きチキン XLデラックスセット", "price_jp": 1053}, {"id": "32", "name_zh": "花生熔岩咔啦雞腿堡L絕配餐", "name_en": "Peanut Lava Crispy Chicken Burger L Perfect Meal", "price": 192, "category": "個人餐", "name_jp": "ピーナッツラバクリスピーチキンバーガー Lパーフェクトセット", "price_jp": 864}, {"id": "33", "name_zh": "花生熔岩咔啦雞腿堡XL超豪肯！餐", "name_en": "Peanut Lava Crispy Chicken Burger XL Deluxe Meal", "price": 242, "category": "個人餐", "name_jp": "ピーナッツラバクリスピーチキンバーガー XLデラックスセット", "price_jp": 1089}, {"id": "39", "name_zh": "蜂蜜風味蒜醬脆雞XL全明星餐", "name_en": "<PERSON> Garlic Crispy Chicken XL All-Star Meal", "price": 253, "category": "個人餐", "name_jp": "ハニーガーリッククリスピーチキン XLオールスターセット", "price_jp": 1138}, {"id": "40", "name_zh": "雙層青花椒咔啦雞腿堡XL全明星餐", "name_en": "Double Sichuan Peppercorn Crispy Chicken Burger XL All-Star Meal", "price": 270, "category": "個人餐", "name_jp": "ダブル四川花椒風味クリスピーチキンバーガー XLオールスターセット", "price_jp": 1215}, {"id": "22", "name_zh": "雪花起司脆雞L絕配餐", "name_en": "Snowflake Cheese Crispy Chicken L Perfect Meal", "price": 201, "category": "個人餐", "name_jp": "スノーフレークチーズクリスピーチキン Lパーフェクトセット", "price_jp": 904}, {"id": "23", "name_zh": "雪花起司脆雞XL餐", "name_en": "Snowflake Cheese Crispy Chicken XL Meal", "price": 241, "category": "個人餐", "name_jp": "スノーフレークチーズクリスピーチキン XLセット", "price_jp": 1084}, {"id": "38", "name_zh": "青花椒全明星個人餐", "name_en": "Sichuan Peppercorn All-Star Individual Meal", "price": 199, "category": "個人餐", "name_jp": "四川花椒風味オールスター個人セット", "price_jp": 896}, {"id": "34", "name_zh": "青花椒香麻咔啦雞腿堡L絕配餐", "name_en": "Sichuan Peppercorn Crispy Chicken Burger L Perfect Meal", "price": 170, "category": "個人餐", "name_jp": "四川花椒風味クリスピーチキンバーガー Lパーフェクトセット", "price_jp": 765}, {"id": "35", "name_zh": "青花椒香麻咔啦雞腿堡XL套餐", "name_en": "Sichuan Peppercorn Crispy Chicken Burger XL Meal", "price": 220, "category": "個人餐", "name_jp": "四川花椒風味クリスピーチキンバーガー XLセット", "price_jp": 990}, {"id": "26", "name_zh": "青花椒香麻脆雞L絕配餐", "name_en": "Sichuan Peppercorn Crispy Chicken L Perfect Meal", "price": 201, "category": "個人餐", "name_jp": "四川花椒風味クリスピーチキン Lパーフェクトセット", "price_jp": 904}, {"id": "27", "name_zh": "青花椒香麻脆雞XL超豪肯！餐", "name_en": "Sichuan Peppercorn Crispy Chicken XL Deluxe Meal", "price": 241, "category": "個人餐", "name_jp": "四川花椒風味クリスピーチキン XLデラックスセット", "price_jp": 1084}, {"id": "24", "name_zh": "黃金魚子海陸Q蝦堡L絕配餐", "name_en": "Golden Roe Surf & Turf Shrimp Burger L Perfect Meal", "price": 208, "category": "個人餐", "name_jp": "ゴールデンロー海鮮エビバーガー Lパーフェクトセット", "price_jp": 936}, {"id": "25", "name_zh": "黃金魚子海陸Q蝦堡XL超豪肯！餐", "name_en": "Golden Roe Surf & Turf Shrimp Burger XL Deluxe Meal", "price": 258, "category": "個人餐", "name_jp": "ゴールデンロー海鮮エビバーガー XLデラックスセット", "price_jp": 1161}]}, {"category": "多人餐/炸雞桶", "items": [{"id": "47", "name_zh": "5塊雞桶", "name_en": "5-pc Chicken Bucket", "price": 299, "category": "多人餐/炸雞桶", "name_jp": "チキン5ピースバケツ", "price_jp": 1346}, {"id": "49", "name_zh": "7塊咔啦脆雞", "name_en": "7-pc <PERSON><PERSON><PERSON> Chicken", "price": 299, "category": "多人餐/炸雞桶", "name_jp": "カーネルクリスピー7ピース", "price_jp": 1346}, {"id": "50", "name_zh": "外送限定優惠- 6塊雞買一桶送一桶", "name_en": "Delivery Special - Buy One Get One Free 6-pc Chicken Bucket", "price": 456, "category": "多人餐/炸雞桶", "name_jp": "デリバリー限定 - チキン6ピースバケツ買一つ得一つ", "price_jp": 2052}, {"id": "46", "name_zh": "好食雞同樂餐", "name_en": "Tasty Chicken Sharing Meal", "price": 1207, "category": "多人餐/炸雞桶", "name_jp": "美味チキンシェアリングセット", "price_jp": 5432}, {"id": "43", "name_zh": "宅宅快樂餐", "name_en": "Homebody Happy Meal", "price": 617, "category": "多人餐/炸雞桶", "name_jp": "ホームボディハッピーセット", "price_jp": 2776}, {"id": "42", "name_zh": "歡聚樂享餐", "name_en": "Happy Gathering Meal", "price": 650, "category": "多人餐/炸雞桶", "name_jp": "ハッピーギャザリングセット", "price_jp": 2925}, {"id": "48", "name_zh": "狂饗桶-5塊雞", "name_en": "Feast Bucket - 5-pc Chicken", "price": 370, "category": "多人餐/炸雞桶", "name_jp": "フィーストバケツ - チキン5ピース", "price_jp": 1665}, {"id": "44", "name_zh": "義式香草紙包雞同饗餐", "name_en": "Italian Herb Paper-Wrapped Chicken Sharing Meal", "price": 925, "category": "多人餐/炸雞桶", "name_jp": "イタリアンハーブ包み焼きチキン シェアリングセット", "price_jp": 4162}, {"id": "45", "name_zh": "超人氣同樂餐", "name_en": "Super Popular Sharing Meal", "price": 924, "category": "多人餐/炸雞桶", "name_jp": "スーパー人気シェアリングセット", "price_jp": 4158}]}, {"category": "配餐", "items": [{"id": "52", "name_zh": "A經典配餐", "name_en": "A Classic Side Meal", "price": 55, "category": "配餐", "name_jp": "Aクラシックサイドセット", "price_jp": 248}, {"id": "53", "name_zh": "B雞汁飯配餐", "name_en": "B Chicken Gravy Rice Side Meal", "price": 55, "category": "配餐", "name_jp": "Bチキングレイビーライスサイドセット", "price_jp": 248}, {"id": "54", "name_zh": "C雙享配餐", "name_en": "C Double Enjoyment Side Meal", "price": 80, "category": "配餐", "name_jp": "Cダブルエンジョイメントサイドセット", "price_jp": 360}, {"id": "51", "name_zh": "XL全明星配餐", "name_en": "XL All-Star Side Meal", "price": 115, "category": "配餐", "name_jp": "XLオールスターサイドセット", "price_jp": 518}]}, {"category": "點心/小食", "items": [{"id": "55", "name_zh": "上校雞塊(4塊)", "name_en": "Colonel's Nuggets (4-pc)", "price": 49, "category": "點心/小食", "name_jp": "カーネルナゲット（4ピース）", "price_jp": 220}, {"id": "56", "name_zh": "上校雞塊(8塊)", "name_en": "Colonel's Nuggets (8-pc)", "price": 98, "category": "點心/小食", "name_jp": "カーネルナゲット（8ピース）", "price_jp": 441}, {"id": "57", "name_zh": "勁爆雞米花", "name_en": "Popcorn Chicken", "price": 49, "category": "點心/小食", "name_jp": "ポップコーンチキン", "price_jp": 220}, {"id": "62", "name_zh": "原味蛋撻", "name_en": "Original Egg Tart", "price": 45, "category": "點心/小食", "name_jp": "オリジナルエッグタルト", "price_jp": 202}, {"id": "64", "name_zh": "原味蛋撻禮盒(6入)", "name_en": "Original Egg Tart Gift Box (6-pc)", "price": 247, "category": "點心/小食", "name_jp": "オリジナルエッグタルトギフトボックス（6個入り）", "price_jp": 1112}, {"id": "63", "name_zh": "檸檬蛋撻", "name_en": "Lemon Egg Tart", "price": 45, "category": "點心/小食", "name_jp": "レモンエッグタルト", "price_jp": 202}, {"id": "67", "name_zh": "玉米濃湯(中)", "name_en": "<PERSON><PERSON> (Medium)", "price": 47, "category": "點心/小食", "name_jp": "コーンスープ（M）", "price_jp": 212}, {"id": "66", "name_zh": "玉米濃湯(小)", "name_en": "<PERSON><PERSON> (Small)", "price": 37, "category": "點心/小食", "name_jp": "コーンスープ（S）", "price_jp": 166}, {"id": "61", "name_zh": "雙份薯條", "name_en": "Double Fries", "price": 40, "category": "點心/小食", "name_jp": "ダブルポテト", "price_jp": 180}, {"id": "65", "name_zh": "雙色蛋撻(原+檸)禮盒", "name_en": "Duo Egg Tart (Original + Lemon) Gift Box", "price": 271, "category": "點心/小食", "name_jp": "デュオエッグタルト（オリジナル+レモン）ギフトボックス", "price_jp": 1220}, {"id": "68", "name_zh": "雞汁風味飯", "name_en": "Chicken Gravy Flavored Rice", "price": 55, "category": "點心/小食", "name_jp": "チキングレイビー風味ライス", "price_jp": 248}, {"id": "59", "name_zh": "香酥脆薯(中)", "name_en": "<PERSON><PERSON><PERSON> (Medium)", "price": 50, "category": "點心/小食", "name_jp": "クリスピーポテト（M）", "price_jp": 225}, {"id": "60", "name_zh": "香酥脆薯(大)", "name_en": "<PERSON><PERSON><PERSON> (Large)", "price": 60, "category": "點心/小食", "name_jp": "クリスピーポテト（L）", "price_jp": 270}, {"id": "58", "name_zh": "香酥脆薯(小)", "name_en": "<PERSON><PERSON><PERSON> (Small)", "price": 40, "category": "點心/小食", "name_jp": "クリスピーポテト（S）", "price_jp": 180}, {"id": "69", "name_zh": "鱈魚圈圈", "name_en": "Cod Fish Rings", "price": 60, "category": "點心/小食", "name_jp": "タラフィッシュリング", "price_jp": 270}]}, {"category": "飲料", "items": [{"id": "73", "name_zh": "冰檸檬紅茶(小)", "name_en": "Iced Lemon Tea (Small)", "price": 30, "category": "飲料", "name_jp": "アイスレモンティー（S）", "price_jp": 135}, {"id": "71", "name_zh": "百事可樂(中)", "name_en": "Pepsi (Medium)", "price": 40, "category": "飲料", "name_jp": "ペプシ（M）", "price_jp": 180}, {"id": "72", "name_zh": "百事可樂(大)", "name_en": "Pepsi (Large)", "price": 50, "category": "飲料", "name_jp": "ペプシ（L）", "price_jp": 225}, {"id": "70", "name_zh": "百事可樂(小)", "name_en": "P<PERSON>si (Small)", "price": 30, "category": "飲料", "name_jp": "ペプシ（S）", "price_jp": 135}]}, {"category": "蛋撻", "items": [{"id": "74", "name_zh": "原味蛋撻", "name_en": "Original Egg Tart", "price": 45, "category": "蛋撻", "name_jp": "オリジナルエッグタルト", "price_jp": 202}, {"id": "76", "name_zh": "原味蛋撻禮盒(6入)", "name_en": "Original Egg Tart Gift Box (6-pc)", "price": 247, "category": "蛋撻", "name_jp": "オリジナルエッグタルトギフトボックス（6個入り）", "price_jp": 1112}, {"id": "75", "name_zh": "檸檬蛋撻", "name_en": "Lemon Egg Tart", "price": 45, "category": "蛋撻", "name_jp": "レモンエッグタルト", "price_jp": 202}, {"id": "77", "name_zh": "雙色蛋撻(原+檸)禮盒", "name_en": "Duo Egg Tart (Original + Lemon) Gift Box", "price": 271, "category": "蛋撻", "name_jp": "デュオエッグタルト（オリジナル+レモン）ギフトボックス", "price_jp": 1220}]}, {"category": "早餐菜單", "items": [{"id": "87", "name_zh": "吮指嫩雞燒餅", "name_en": "Finger Lickin' Tender Chicken Pancake", "price": 56, "category": "早餐菜單", "name_jp": "フィンガーリッキンテンダーチキンパンケーキ", "price_jp": 252}, {"id": "93", "name_zh": "吮指嫩雞燒餅套餐", "name_en": "Finger Lickin' Tender Chicken Pancake Meal", "price": 75, "category": "早餐菜單", "name_jp": "フィンガーリッキンテンダーチキンパンケーキセット", "price_jp": 338}, {"id": "86", "name_zh": "吮指嫩雞蛋堡", "name_en": "Finger Lickin' Tender Chicken Egg Burger", "price": 50, "category": "早餐菜單", "name_jp": "フィンガーリッキンテンダーチキンエッグバーガー", "price_jp": 225}, {"id": "92", "name_zh": "吮指嫩雞蛋堡套餐", "name_en": "Finger Lickin' Tender Chicken Egg Burger Meal", "price": 69, "category": "早餐菜單", "name_jp": "フィンガーリッキンテンダーチキンエッグバーガーセット", "price_jp": 310}, {"id": "81", "name_zh": "吮指嫩雞蛋捲餅", "name_en": "Finger Lickin' Tender Chicken Egg Roll", "price": 66, "category": "早餐菜單", "name_jp": "フィンガーリッキンテンダーチキンエッグロール", "price_jp": 297}, {"id": "95", "name_zh": "吮指嫩雞蛋捲餅套餐", "name_en": "Finger Lickin' Tender Chicken Egg Roll Meal", "price": 85, "category": "早餐菜單", "name_jp": "フィンガーリッキンテンダーチキンエッグロールセット", "price_jp": 382}, {"id": "80", "name_zh": "培根薯餅蛋捲", "name_en": "Bacon Hash Brown Egg Roll", "price": 56, "category": "早餐菜單", "name_jp": "ベーコンハッシュブラウンエッグロール", "price_jp": 252}, {"id": "96", "name_zh": "培根薯餅蛋捲餅套餐", "name_en": "<PERSON> Hash Brown Egg Roll Meal", "price": 75, "category": "早餐菜單", "name_jp": "ベーコンハッシュブラウンエッグロールセット", "price_jp": 338}, {"id": "79", "name_zh": "紅藜燕麥嫩雞粥", "name_en": "Red Quinoa Oatmeal Tender Chicken Porridge", "price": 46, "category": "早餐菜單", "name_jp": "赤キヌアオートミールテンダーチキンお粥", "price_jp": 207}, {"id": "94", "name_zh": "紅藜燕麥嫩雞粥套餐", "name_en": "Red Quinoa Oatmeal Tender Chicken Porridge Meal", "price": 65, "category": "早餐菜單", "name_jp": "赤キヌアオートミールテンダーチキンお粥セット", "price_jp": 292}, {"id": "82", "name_zh": "總匯歐姆蛋燒餅", "name_en": "Club Omelette Pancake", "price": 50, "category": "早餐菜單", "name_jp": "クラブオムレツパンケーキ", "price_jp": 225}, {"id": "91", "name_zh": "總匯歐姆蛋燒餅套餐", "name_en": "Club Omelette Pancake Meal", "price": 69, "category": "早餐菜單", "name_jp": "クラブオムレツパンケーキセット", "price_jp": 310}, {"id": "85", "name_zh": "花生吮指嫩雞蛋堡", "name_en": "Peanut Finger Lickin' Tender Chicken Egg Burger", "price": 60, "category": "早餐菜單", "name_jp": "ピーナッツフィンガーリッキンテンダーチキンエッグバーガー", "price_jp": 270}, {"id": "89", "name_zh": "花生吮指嫩雞蛋堡套餐", "name_en": "Peanut Finger Lickin' Tender Chicken Egg Burger Meal", "price": 79, "category": "早餐菜單", "name_jp": "ピーナッツフィンガーリッキンテンダーチキンエッグバーガーセット", "price_jp": 356}, {"id": "97", "name_zh": "花生好事雙堡餐", "name_en": "Peanut Double Burger Meal", "price": 119, "category": "早餐菜單", "name_jp": "ピーナッツダブルバーガーセット", "price_jp": 536}, {"id": "84", "name_zh": "花生肉鬆嫩雞蛋堡", "name_en": "Peanut Pork Floss Tender Chicken Egg Burger", "price": 60, "category": "早餐菜單", "name_jp": "ピーナッツポークフロステンダーチキンエッグバーガー", "price_jp": 270}, {"id": "90", "name_zh": "花生肉鬆嫩雞蛋堡套餐", "name_en": "Peanut Pork Floss Tender Chicken Egg Burger Meal", "price": 79, "category": "早餐菜單", "name_jp": "ピーナッツポークフロステンダーチキンエッグバーガーセット", "price_jp": 356}, {"id": "83", "name_zh": "花生起司蛋堡", "name_en": "Peanut Cheese Egg Burger", "price": 35, "category": "早餐菜單", "name_jp": "ピーナッツチーズエッグバーガー", "price_jp": 158}, {"id": "88", "name_zh": "花生起司蛋堡套餐", "name_en": "Peanut Cheese Egg Burger Meal", "price": 49, "category": "早餐菜單", "name_jp": "ピーナッツチーズエッグバーガーセット", "price_jp": 220}, {"id": "78", "name_zh": "薯餅", "name_en": "<PERSON><PERSON>", "price": 25, "category": "早餐菜單", "name_jp": "ハッシュブラウン", "price_jp": 112}, {"id": "98", "name_zh": "雙雞特享雙人餐", "name_en": "Double Chicken Special Duo Meal", "price": 138, "category": "早餐菜單", "name_jp": "ダブルチキンスペシャルデュオセット", "price_jp": 621}]}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-03T10:53:47.245Z", "aiGenerated": true}}