{"prompt": "あなたは自然言語で注文を受け付けるハンバーガーショップの注文受付システムです。顧客の注文を理解し、確認し、注文を確定またはキャンセルします。提供可能なメニューは以下に示されています。顧客の発言に基づいて適切な応答を生成してください。", "parameters": {"feature": "自然言語順序付け", "scenario": "お客様からキャンセル注文", "given": ["顧客が注文ページにいる", "システムは確認のために識別された項目を提示しました", "システムは確認のために識別された商品（ビッグマック エクストラバリューミール）を提示しました", "システムは確認のために識別された商品（ビッグマック エクストラバリューミール、コーンスープ）を提示しました", "システムは確認のための最終注文を提示しました(ビッグマックの余分な価値の食事、コーラ)", "システムは確認のために識別された項目を提示しました(ビッグマックの余分な価値の食事、コーンスープ)"], "when": ["顧客は自然言語入力フィールドに「ビッグマックとコーンスープを注文したい」と入力する。", "顧客は音声入力を使用して「マッククリスピーチキン（2ピース）エクストラバリューミールを希望します」と言います。", "顧客は「コーンスープをコーラに変更しました」と言います", "顧客が「不思議な魔法のバーガーをください」と入力する", "顧客は「フライドポテトを1つ追加してください」と言います", "顧客は「コーンスープはもうありません」と言います", "顧客が「チーズバーガーとパイが欲しい」と入力する", "顧客が「ビッグマックとは何ですか？」と入力します。", "顧客と話し、「確定」或「大丈夫だ、问题ない」", "顧客と話し、『キャンセル』或「もうそれはいらない」"], "then": ["システムは「ビッグマック エクストラバリューミール」と「コーンスープ」を識別する必要があります。", "識別された商品とその詳細を自然言語を使用して顧客に確認のために提示します（例：「ビッグマック エクストラバリューセットとコーンスープを注文しますか？」）", "システムは音声入力をテキストに書き起こす必要がある", "変更した注文（ビッグマック エクストラバリューセット、コーラ）を自然言語で顧客に提示し、確認する", "システムは「ミステリアスマジックバーガー」というアイテムが見つからなかったことを示すはずです", "メニューを確認するか、リクエストを言い換えることを提案する", "確認のために自然言語を使用して、更新された注文（ビッグマック エクストラ バリュー ミール、フライドポテト）を顧客に提示します（例：「わかりました。フライドポテトを 1 つ追加しました。これでビッグマック エクストラ バリュー ミールとフライドポテト 1 つが含まれます。よろしいですか？」）", "システムは提案された注文から「コーンスープ」を削除する必要がある", "システムは「チーズバーガー」を認識するはずだ", "「ある種のパイ」という項目が見つからなかったことを示す", "システムはデータベース/RAG に「ビッグマック」の詳細 (説明、材料など) を照会する必要があります。", "「ビッグマック」に関する情報を自然言語を使用して顧客に提示する（例：「ビッグマックは、...を含むクラシックなダブルビーフバーガーです」）", "システムは注文を処理すべきです", "注文確認メッセージ(例:「ご注文が送信されました!」)を表示します。", "システムは現在の注文をキャンセルする必要があります", "キャンセルを示すメッセージを表示します（例：「ご注文はキャンセルされました。」）"], "menu": [{"category": "バリューセット", "items": [{"id": "1", "name_jp": "ビッグマック® セット", "price_jp": 750}, {"id": "2", "name_jp": "ダブルチーズバーガー セット", "price_jp": 700}, {"id": "3", "name_jp": "グリルドチキンバーガー セット", "price_jp": 690}, {"id": "4", "name_jp": "マックチキン® セット", "price_jp": 500}, {"id": "7", "name_jp": "マックチキン® セット", "price_jp": 500}, {"id": "9", "name_jp": "倍マックチキン セット", "price_jp": 620}, {"id": "10", "name_jp": "フィレオフィッシュ® セット", "price_jp": 680}, {"id": "11", "name_jp": "クォーターパウンダー・チーズ セット", "price_jp": 780}, {"id": "12", "name_jp": "ダブルクォーターパウンダー・チーズ セット", "price_jp": 980}, {"id": "13", "name_jp": "アンガスビーフ BLT セット", "price_jp": 850}, {"id": "14", "name_jp": "グリルドチキン BLT セット", "price_jp": 800}, {"id": "15", "name_jp": "アンガスビーフ マッシュルーム セット", "price_jp": 850}, {"id": "16", "name_jp": "チキン マッシュルーム セット", "price_jp": 800}, {"id": "17", "name_jp": "アンガスビーフ パルメザン セット", "price_jp": 850}, {"id": "18", "name_jp": "チキン パルメザン セット", "price_jp": 800}]}, {"category": "フライドチキン", "items": [{"id": "5", "name_jp": "チキンマックナゲット® 6ピース セット", "price_jp": 650}, {"id": "6", "name_jp": "チキンマックナゲット® 10ピース セット", "price_jp": 740}, {"id": "8", "name_jp": "マックフライドチキン 2ピース セット", "price_jp": 720}]}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-04T01:11:53.673Z", "aiGenerated": true}}