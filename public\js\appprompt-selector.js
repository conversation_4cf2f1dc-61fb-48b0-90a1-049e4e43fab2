// APPprompt 選擇器功能

// 全局變量
let appPromptFiles = [];

// 初始化 APPprompt 選擇器
function initAppPromptSelector() {
    console.log('初始化 APPprompt 選擇器');
    
    // 綁定事件監聽器
    const refreshBtn = document.getElementById('refresh-appprompt-btn');
    const showInfoBtn = document.getElementById('show-appprompt-info-btn');
    const selector = document.getElementById('appprompt-selector');
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadAppPromptFiles);
    }
    
    if (showInfoBtn) {
        showInfoBtn.addEventListener('click', toggleAppPromptInfo);
    }
    
    if (selector) {
        selector.addEventListener('change', onAppPromptSelectionChange);
    }
    
    // 初始載入 APPprompt 文件列表
    loadAppPromptFiles();
}

// 載入 APPprompt 文件列表
async function loadAppPromptFiles() {
    try {
        console.log('正在載入 APPprompt 文件列表...');

        const refreshBtn = document.getElementById('refresh-appprompt-btn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 載入中...';
        }

        console.log('發送請求到 /api/prompt/list-appprompt-files');
        const response = await fetch('/api/prompt/list-appprompt-files');
        console.log('收到回應，狀態:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP 錯誤! 狀態: ${response.status}`);
        }

        const result = await response.json();
        console.log('解析的結果:', result);

        if (result.success) {
            appPromptFiles = result.files;
            updateAppPromptSelector();
            console.log(`載入了 ${appPromptFiles.length} 個 APPprompt 文件`);

            if (typeof showToastMessage === 'function') {
                showToastMessage(`已載入 ${appPromptFiles.length} 個 APPprompt 文件`, 'success');
            } else {
                console.log('showToastMessage 函數不存在');
            }
        } else {
            console.error('載入 APPprompt 文件失敗:', result.error);
            if (typeof showToastMessage === 'function') {
                showToastMessage('載入 APPprompt 文件失敗', 'error');
            } else {
                alert('載入 APPprompt 文件失敗: ' + result.error);
            }
        }
    } catch (error) {
        console.error('載入 APPprompt 文件時發生錯誤:', error);
        if (typeof showToastMessage === 'function') {
            showToastMessage('載入 APPprompt 文件時發生錯誤', 'error');
        } else {
            alert('載入 APPprompt 文件時發生錯誤: ' + error.message);
        }
    } finally {
        const refreshBtn = document.getElementById('refresh-appprompt-btn');
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-refresh"></i> 重新載入';
        }
    }
}

// 更新 APPprompt 選擇器
function updateAppPromptSelector() {
    const selector = document.getElementById('appprompt-selector');
    if (!selector) return;
    
    // 清空現有選項
    selector.innerHTML = '<option value="">使用最新的 APPprompt</option>';
    
    // 添加文件選項
    appPromptFiles.forEach(file => {
        const option = document.createElement('option');
        option.value = file.filename;
        
        // 構建選項文本
        let optionText = file.filename;
        
        if (file.menuInfo) {
            optionText += ` (${file.menuInfo.restaurantName}, ${file.menuInfo.totalItems} 項餐點)`;
        }
        
        if (file.metadata && file.metadata.generatedAt) {
            const date = new Date(file.metadata.generatedAt);
            optionText += ` - ${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
        } else {
            const date = new Date(file.createdAt);
            optionText += ` - ${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
        }
        
        option.textContent = optionText;
        selector.appendChild(option);
    });
}

// 當 APPprompt 選擇改變時
function onAppPromptSelectionChange() {
    const selector = document.getElementById('appprompt-selector');
    const selectedFile = selector?.value;
    
    if (selectedFile) {
        console.log('選擇了 APPprompt 文件:', selectedFile);
        
        // 找到選擇的文件信息
        const fileInfo = appPromptFiles.find(f => f.filename === selectedFile);
        if (fileInfo && typeof showToastMessage === 'function') {
            let message = `已選擇 APPprompt: ${selectedFile}`;
            if (fileInfo.menuInfo) {
                message += ` (${fileInfo.menuInfo.restaurantName})`;
            }
            showToastMessage(message, 'info');
        }
    } else {
        console.log('選擇使用最新的 APPprompt');
        if (typeof showToastMessage === 'function') {
            showToastMessage('將使用最新的 APPprompt', 'info');
        }
    }
    
    // 如果詳細信息面板是打開的，更新它
    const infoPanel = document.getElementById('appprompt-info');
    if (infoPanel && infoPanel.style.display !== 'none') {
        showAppPromptInfo();
    }
}

// 切換 APPprompt 詳細信息顯示
function toggleAppPromptInfo() {
    const infoPanel = document.getElementById('appprompt-info');
    if (!infoPanel) return;
    
    if (infoPanel.style.display === 'none' || !infoPanel.style.display) {
        showAppPromptInfo();
    } else {
        hideAppPromptInfo();
    }
}

// 顯示 APPprompt 詳細信息
function showAppPromptInfo() {
    const infoPanel = document.getElementById('appprompt-info');
    const selector = document.getElementById('appprompt-selector');
    const showInfoBtn = document.getElementById('show-appprompt-info-btn');
    
    if (!infoPanel || !selector) return;
    
    const selectedFile = selector.value;
    
    if (!selectedFile) {
        // 顯示最新 APPprompt 的信息
        infoPanel.innerHTML = `
            <h4>使用最新的 APPprompt</h4>
            <p>系統將自動使用最新生成的 APPprompt 文件進行自然語言點餐處理。</p>
        `;
    } else {
        // 顯示選擇文件的詳細信息
        const fileInfo = appPromptFiles.find(f => f.filename === selectedFile);
        if (fileInfo) {
            infoPanel.innerHTML = generateFileInfoHTML(fileInfo);
        } else {
            infoPanel.innerHTML = '<p>找不到文件信息</p>';
        }
    }
    
    infoPanel.style.display = 'block';
    
    if (showInfoBtn) {
        showInfoBtn.innerHTML = '<i class="fas fa-eye-slash"></i> 隱藏詳細信息';
    }
}

// 隱藏 APPprompt 詳細信息
function hideAppPromptInfo() {
    const infoPanel = document.getElementById('appprompt-info');
    const showInfoBtn = document.getElementById('show-appprompt-info-btn');
    
    if (infoPanel) {
        infoPanel.style.display = 'none';
    }
    
    if (showInfoBtn) {
        showInfoBtn.innerHTML = '<i class="fas fa-info-circle"></i> 詳細信息';
    }
}

// 生成文件信息 HTML
function generateFileInfoHTML(fileInfo) {
    let html = `<h4>APPprompt 文件詳細信息</h4>`;
    
    html += '<div class="info-grid">';
    
    // 文件信息
    html += `
        <div class="info-item file-info">
            <strong>文件信息</strong>
            <div>文件名: ${fileInfo.filename}</div>
            <div>大小: ${formatFileSize(fileInfo.size)}</div>
            <div>創建時間: ${new Date(fileInfo.createdAt).toLocaleString()}</div>
            <div>修改時間: ${new Date(fileInfo.modifiedAt).toLocaleString()}</div>
        </div>
    `;
    
    // 菜單信息
    if (fileInfo.menuInfo) {
        html += `
            <div class="info-item menu-info">
                <strong>菜單信息</strong>
                <div>餐廳: ${fileInfo.menuInfo.restaurantName}</div>
                <div>總餐點數: ${fileInfo.menuInfo.totalItems}</div>
                <div>分類數: ${fileInfo.menuInfo.categories}</div>
            </div>
        `;
    }
    
    // 生成信息
    if (fileInfo.metadata) {
        html += `
            <div class="info-item metadata-info">
                <strong>生成信息</strong>
                <div>來源: ${fileInfo.metadata.source}</div>
                <div>AI 生成: ${fileInfo.metadata.aiGenerated ? '是' : '否'}</div>
                <div>生成時間: ${new Date(fileInfo.metadata.generatedAt).toLocaleString()}</div>
            </div>
        `;
    }
    
    html += '</div>';
    
    if (fileInfo.parseError) {
        html += '<div class="alert alert-warning" style="margin-top: 10px;">⚠️ 此文件可能已損壞或格式不正確</div>';
    }
    
    return html;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
}

// 當頁面載入完成時初始化
document.addEventListener('DOMContentLoaded', function() {
    // 檢查是否在自然語言點餐頁面
    const naturalOrderTab = document.getElementById('natural-order');
    if (naturalOrderTab) {
        initAppPromptSelector();
    }
});

// 當切換到自然語言點餐頁面時也初始化
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('tab') && event.target.dataset.tab === 'natural-order') {
        setTimeout(() => {
            initAppPromptSelector();
        }, 100);
    }
});
